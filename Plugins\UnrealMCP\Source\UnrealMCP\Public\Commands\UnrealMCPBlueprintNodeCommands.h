#pragma once

#include "CoreMinimal.h"
#include "Json.h"

/**
 * Handler class for Blueprint Node-related MCP commands
 */
class UNREALMCP_API FUnrealMCPBlueprintNodeCommands
{
public:
    FUnrealMCPBlueprintNodeCommands();

    // Handle blueprint node commands
    TSharedPtr<FJsonObject> HandleCommand(const FString &CommandType, const TSharedPtr<FJsonObject> &Params);

private:
    // Specific blueprint node command handlers
    TSharedPtr<FJsonObject> HandleConnectBlueprintNodes(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintGetSelfComponentReference(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintEvent(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintFunctionCall(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintVariable(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintInputActionNode(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSelfReference(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleFindBlueprintNodes(const TSharedPtr<FJsonObject> &Params);

    // Array operation handlers
    TSharedPtr<FJsonObject> HandleAddBlueprintArrayFunction(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintForLoop(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintForEachLoop(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintVariableGet(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintVariableSet(const TSharedPtr<FJsonObject> &Params);

    // Component and array operation handlers
    TSharedPtr<FJsonObject> HandleAddBlueprintDestroyComponent(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintArrayGet(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintArrayLength(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintArrayClear(const TSharedPtr<FJsonObject> &Params);

    // Spline-specific node handlers
    TSharedPtr<FJsonObject> HandleAddBlueprintSplineGetNumberOfPoints(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSplineGetLocationAtPoint(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSplineGetTangentAtPoint(const TSharedPtr<FJsonObject> &Params);

    // Math operation handlers
    TSharedPtr<FJsonObject> HandleAddBlueprintMathOperation(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintBranchNode(const TSharedPtr<FJsonObject> &Params);

    // SplineMeshComponent handlers
    TSharedPtr<FJsonObject> HandleAddBlueprintAddSplineMeshComponent(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSplineMeshSetStartAndEnd(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSplineMeshSetScale(const TSharedPtr<FJsonObject> &Params);

    // Component configuration handlers
    TSharedPtr<FJsonObject> HandleAddBlueprintSetMobility(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSetCollisionEnabled(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSetCollisionProfile(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintSetCanAffectNavigation(const TSharedPtr<FJsonObject> &Params);
    TSharedPtr<FJsonObject> HandleAddBlueprintArrayAdd(const TSharedPtr<FJsonObject> &Params);
};