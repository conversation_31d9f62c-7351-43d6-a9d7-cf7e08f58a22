[{"file": "C:\\Auracron\\Plugins\\Marketplace\\LudusAI\\Source\\LudusChatUI\\LudusChatUI.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\LudusChatUI.173.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\Marketplace\\LudusAI\\Source\\LudusClient\\LudusClient.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\LudusClient.172.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\Marketplace\\LudusAI\\Source\\LudusCore\\LudusCore.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\LudusCore.171.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\Marketplace\\LudusAI\\Source\\LudusDatabase\\LudusDatabase.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\LudusDatabase.176.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\Marketplace\\LudusAI\\Source\\LudusEditor\\LudusEditor.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\LudusEditor.174.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\Marketplace\\LudusAI\\Source\\LudusMarkdown\\LudusMarkdown.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\LudusMarkdown.175.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\Commands\\UnrealMCPBlueprintCommands.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\Commands\\UnrealMCPBlueprintNodeCommands.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\Commands\\UnrealMCPCommonUtils.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\Commands\\UnrealMCPEditorCommands.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\Commands\\UnrealMCPProjectCommands.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\Commands\\UnrealMCPSplineCommands.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\Commands\\UnrealMCPUMGCommands.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\MCPServerRunnable.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\UnrealMCPBridge.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Private\\UnrealMCPModule.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\Commands\\UnrealMCPBlueprintCommands.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\Commands\\UnrealMCPBlueprintNodeCommands.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\Commands\\UnrealMCPCommonUtils.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\Commands\\UnrealMCPEditorCommands.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\Commands\\UnrealMCPProjectCommands.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\Commands\\UnrealMCPSplineCommands.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\Commands\\UnrealMCPUMGCommands.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\MCPServerRunnable.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\UnrealMCPBridge.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\Public\\UnrealMCPModule.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Plugins\\UnrealMCP\\Source\\UnrealMCP\\UnrealMCP.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\UnrealMCP.47.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Source\\EnvDesigns\\EnvDesigns.Build.cs", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\EnvDesigns.416.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Source\\EnvDesigns\\EnvDesigns.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\EnvDesigns.416.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Source\\EnvDesigns\\EnvDesigns.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\EnvDesigns.416.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Source\\EnvDesigns\\EnvDesignsCharacter.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\EnvDesigns.416.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Source\\EnvDesigns\\EnvDesignsCharacter.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\EnvDesigns.416.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Source\\EnvDesigns\\EnvDesignsGameMode.cpp", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\EnvDesigns.416.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}, {"file": "C:\\Auracron\\Source\\EnvDesigns\\EnvDesignsGameMode.h", "arguments": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default\\EnvDesigns.416.rsp"], "directory": "C:\\Program Files\\Epic Games\\UE_5.6\\Engine\\Source"}]