"""
Blueprint Node Tools for Unreal MCP.

This module provides tools for manipulating Blueprint graph nodes and connections.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_blueprint_node_tools(mcp: FastMCP):
    """Register Blueprint node manipulation tools with the MCP server."""
    
    @mcp.tool()
    def add_blueprint_event_node(
        ctx: Context,
        blueprint_name: str,
        event_name: str,
        node_position = None
    ) -> Dict[str, Any]:
        """
        Add an event node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            event_name: Name of the event. Use 'Receive' prefix for standard events:
                       - 'ReceiveBeginPlay' for Begin Play
                       - 'ReceiveTick' for Tick
                       - etc.
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Handle default value within the method body
            if node_position is None:
                node_position = [0, 0]
            
            params = {
                "blueprint_name": blueprint_name,
                "event_name": event_name,
                "node_position": node_position
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding event node '{event_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_event_node", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Event node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding event node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def add_blueprint_input_action_node(
        ctx: Context,
        blueprint_name: str,
        action_name: str,
        node_position = None
    ) -> Dict[str, Any]:
        """
        Add an input action event node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            action_name: Name of the input action to respond to
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Handle default value within the method body
            if node_position is None:
                node_position = [0, 0]
            
            params = {
                "blueprint_name": blueprint_name,
                "action_name": action_name,
                "node_position": node_position
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding input action node for '{action_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_input_action_node", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Input action node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding input action node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def add_blueprint_function_node(
        ctx: Context,
        blueprint_name: str,
        target: str,
        function_name: str,
        params = None,
        node_position = None
    ) -> Dict[str, Any]:
        """
        Add a function call node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            target: Target object for the function (component name or self)
            function_name: Name of the function to call
            params: Optional parameters to set on the function node
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Handle default values within the method body
            if params is None:
                params = {}
            if node_position is None:
                node_position = [0, 0]
            
            command_params = {
                "blueprint_name": blueprint_name,
                "target": target,
                "function_name": function_name,
                "params": params,
                "node_position": node_position
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding function node '{function_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_function_node", command_params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Function node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding function node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
            
    @mcp.tool()
    def connect_blueprint_nodes(
        ctx: Context,
        blueprint_name: str,
        source_node_id: str,
        source_pin: str,
        target_node_id: str,
        target_pin: str
    ) -> Dict[str, Any]:
        """
        Connect two nodes in a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            source_node_id: ID of the source node
            source_pin: Name of the output pin on the source node
            target_node_id: ID of the target node
            target_pin: Name of the input pin on the target node
            
        Returns:
            Response indicating success or failure
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            params = {
                "blueprint_name": blueprint_name,
                "source_node_id": source_node_id,
                "source_pin": source_pin,
                "target_node_id": target_node_id,
                "target_pin": target_pin
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Connecting nodes in blueprint '{blueprint_name}'")
            response = unreal.send_command("connect_blueprint_nodes", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Node connection response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error connecting nodes: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def add_blueprint_variable(
        ctx: Context,
        blueprint_name: str,
        variable_name: str,
        variable_type: str,
        is_exposed: bool = False
    ) -> Dict[str, Any]:
        """
        Add a variable to a Blueprint.
        
        Args:
            blueprint_name: Name of the target Blueprint
            variable_name: Name of the variable
            variable_type: Type of the variable (Boolean, Integer, Float, Vector, etc.)
            is_exposed: Whether to expose the variable to the editor
            
        Returns:
            Response indicating success or failure
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            params = {
                "blueprint_name": blueprint_name,
                "variable_name": variable_name,
                "variable_type": variable_type,
                "is_exposed": is_exposed
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding variable '{variable_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_variable", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Variable creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding variable: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def add_blueprint_get_self_component_reference(
        ctx: Context,
        blueprint_name: str,
        component_name: str,
        node_position = None
    ) -> Dict[str, Any]:
        """
        Add a node that gets a reference to a component owned by the current Blueprint.
        This creates a node similar to what you get when dragging a component from the Components panel.
        
        Args:
            blueprint_name: Name of the target Blueprint
            component_name: Name of the component to get a reference to
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Handle None case explicitly in the function
            if node_position is None:
                node_position = [0, 0]
            
            params = {
                "blueprint_name": blueprint_name,
                "component_name": component_name,
                "node_position": node_position
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding self component reference node for '{component_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_get_self_component_reference", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Self component reference node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding self component reference node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def add_blueprint_self_reference(
        ctx: Context,
        blueprint_name: str,
        node_position = None
    ) -> Dict[str, Any]:
        """
        Add a 'Get Self' node to a Blueprint's event graph that returns a reference to this actor.
        
        Args:
            blueprint_name: Name of the target Blueprint
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if node_position is None:
                node_position = [0, 0]
                
            params = {
                "blueprint_name": blueprint_name,
                "node_position": node_position
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding self reference node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_self_reference", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Self reference node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding self reference node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def find_blueprint_nodes(
        ctx: Context,
        blueprint_name: str,
        node_type = None,
        event_type = None
    ) -> Dict[str, Any]:
        """
        Find nodes in a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            node_type: Optional type of node to find (Event, Function, Variable, etc.)
            event_type: Optional specific event type to find (BeginPlay, Tick, etc.)
            
        Returns:
            Response containing array of found node IDs and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            params = {
                "blueprint_name": blueprint_name,
                "node_type": node_type,
                "event_type": event_type
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Finding nodes in blueprint '{blueprint_name}'")
            response = unreal.send_command("find_blueprint_nodes", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Node find response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error finding nodes: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def add_blueprint_for_loop(
        ctx: Context,
        blueprint_name: str,
        node_position = None
    ) -> Dict[str, Any]:
        """
        Add a For Loop node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Handle default value within the method body
            if node_position is None:
                node_position = [0, 0]
            
            params = {
                "blueprint_name": blueprint_name,
                "node_position": node_position
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding For Loop node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_for_loop", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"For Loop node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding For Loop node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_variable_get(
        ctx: Context,
        blueprint_name: str,
        variable_name: str,
        node_position = None
    ) -> Dict[str, Any]:
        """
        Add a variable GET node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            variable_name: Name of the variable to get
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            # Handle default value within the method body
            if node_position is None:
                node_position = [0, 0]
            
            params = {
                "blueprint_name": blueprint_name,
                "variable_name": variable_name,
                "node_position": node_position
            }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding variable GET node for '{variable_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_variable_get", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Variable GET node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding variable GET node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_for_each_loop(
        ctx: Context,
        blueprint_name: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a For Each Loop node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            params = {
                "blueprint_name": blueprint_name
            }
            
            if node_position:
                params["node_position"] = node_position
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding For Each Loop node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_for_each_loop", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"For Each Loop node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding For Each Loop node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_array_length(
        ctx: Context,
        blueprint_name: str,
        array_variable: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add an Array Length node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            array_variable: Name of the array variable to get length from
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            params = {
                "blueprint_name": blueprint_name,
                "array_variable": array_variable
            }
            
            if node_position:
                params["node_position"] = node_position
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding Array Length node for '{array_variable}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_array_length", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Array Length node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding Array Length node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_array_get(
        ctx: Context,
        blueprint_name: str,
        array_variable: str,
        index: int = 0,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add an Array Get node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            array_variable: Name of the array variable to get from
            index: Index to get from the array (default: 0)
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            params = {
                "blueprint_name": blueprint_name,
                "array_variable": array_variable,
                "index": index
            }
            
            if node_position:
                params["node_position"] = node_position
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding Array Get node for '{array_variable}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_array_get", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Array Get node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding Array Get node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_destroy_component(
        ctx: Context,
        blueprint_name: str,
        component_reference: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a Destroy Component node to a Blueprint's event graph.
        
        Args:
            blueprint_name: Name of the target Blueprint
            component_reference: Reference to the component to destroy
            node_position: Optional [X, Y] position in the graph
            
        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            params = {
                "blueprint_name": blueprint_name,
                "component_reference": component_reference
            }
            
            if node_position:
                params["node_position"] = node_position
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            logger.info(f"Adding Destroy Component node for '{component_reference}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_destroy_component", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Destroy Component node creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error adding Destroy Component node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_array_clear(
        ctx: Context,
        blueprint_name: str,
        array_variable: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add an Array Clear node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            array_variable: Name of the array variable to clear
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "array_variable": array_variable
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Array Clear node for '{array_variable}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_array_clear", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Array Clear node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Array Clear node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_spline_get_tangent_at_point(
        ctx: Context,
        blueprint_name: str,
        spline_component: str,
        point_index: int = 0,
        coordinate_space: str = "World",
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a Spline Get Tangent at Point node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            spline_component: Name of the spline component
            point_index: Index of the spline point
            coordinate_space: Coordinate space (World or Local)
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "spline_component": spline_component,
                "point_index": point_index,
                "coordinate_space": coordinate_space
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Spline Get Tangent at Point node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_spline_get_tangent_at_point", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Spline Get Tangent at Point node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Spline Get Tangent at Point node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_math_operation(
        ctx: Context,
        blueprint_name: str,
        operation: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a math operation node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            operation: Math operation (subtract_int, greater_int, etc.)
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "operation": operation
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding math operation '{operation}' node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_math_operation", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Math operation node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding math operation node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_branch_node(
        ctx: Context,
        blueprint_name: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a Branch node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Branch node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_branch_node", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Branch node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Branch node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_add_spline_mesh_component(
        ctx: Context,
        blueprint_name: str,
        component_name: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a SplineMeshComponent to a Blueprint.

        Args:
            blueprint_name: Name of the target Blueprint
            component_name: Name for the new SplineMeshComponent
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "component_name": component_name
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding SplineMeshComponent '{component_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_add_spline_mesh_component", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"SplineMeshComponent creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding SplineMeshComponent: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_spline_mesh_set_start_and_end(
        ctx: Context,
        blueprint_name: str,
        spline_mesh_component: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a SplineMesh Set Start and End node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            spline_mesh_component: Name of the SplineMeshComponent
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "spline_mesh_component": spline_mesh_component
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding SplineMesh Set Start and End node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_spline_mesh_set_start_and_end", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"SplineMesh Set Start and End node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding SplineMesh Set Start and End node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_spline_mesh_set_scale(
        ctx: Context,
        blueprint_name: str,
        spline_mesh_component: str,
        scale_type: str = "StartScale",
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a SplineMesh Set Scale node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            spline_mesh_component: Name of the SplineMeshComponent
            scale_type: Type of scale to set (StartScale or EndScale)
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "spline_mesh_component": spline_mesh_component,
                "scale_type": scale_type
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding SplineMesh Set Scale node to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_spline_mesh_set_scale", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"SplineMesh Set Scale node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding SplineMesh Set Scale node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_set_mobility(
        ctx: Context,
        blueprint_name: str,
        component_name: str,
        mobility: str = "Movable",
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a Set Mobility node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            component_name: Name of the component to set mobility on
            mobility: Mobility type (Static, Stationary, Movable)
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "component_name": component_name,
                "mobility": mobility
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Set Mobility node for '{component_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_set_mobility", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Set Mobility node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Set Mobility node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_set_collision_enabled(
        ctx: Context,
        blueprint_name: str,
        component_name: str,
        collision_enabled: str = "QueryAndPhysics",
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a Set Collision Enabled node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            component_name: Name of the component to set collision on
            collision_enabled: Collision type (NoCollision, QueryOnly, PhysicsOnly, QueryAndPhysics)
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "component_name": component_name,
                "collision_enabled": collision_enabled
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Set Collision Enabled node for '{component_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_set_collision_enabled", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Set Collision Enabled node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Set Collision Enabled node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_set_collision_profile(
        ctx: Context,
        blueprint_name: str,
        component_name: str,
        collision_profile: str = "BlockAll",
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a Set Collision Profile node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            component_name: Name of the component to set collision profile on
            collision_profile: Collision profile name (BlockAll, NoCollision, etc.)
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "component_name": component_name,
                "collision_profile": collision_profile
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Set Collision Profile node for '{component_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_set_collision_profile", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Set Collision Profile node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Set Collision Profile node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_set_can_affect_navigation(
        ctx: Context,
        blueprint_name: str,
        component_name: str,
        can_affect_navigation: bool = True,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add a Set Can Affect Navigation node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            component_name: Name of the component to set navigation on
            can_affect_navigation: Whether the component can affect navigation
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "component_name": component_name,
                "can_affect_navigation": can_affect_navigation
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Set Can Affect Navigation node for '{component_name}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_set_can_affect_navigation", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Set Can Affect Navigation node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Set Can Affect Navigation node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def add_blueprint_array_add(
        ctx: Context,
        blueprint_name: str,
        array_variable: str,
        node_position: str = None
    ) -> Dict[str, Any]:
        """
        Add an Array Add node to a Blueprint's event graph.

        Args:
            blueprint_name: Name of the target Blueprint
            array_variable: Name of the array variable to add to
            node_position: Optional [X, Y] position in the graph

        Returns:
            Response containing the node ID and success status
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            params = {
                "blueprint_name": blueprint_name,
                "array_variable": array_variable
            }

            if node_position:
                params["node_position"] = node_position

            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            logger.info(f"Adding Array Add node for '{array_variable}' to blueprint '{blueprint_name}'")
            response = unreal.send_command("add_blueprint_array_add", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Array Add node creation response: {response}")
            return response

        except Exception as e:
            error_msg = f"Error adding Array Add node: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    logger.info("Blueprint node tools registered successfully")