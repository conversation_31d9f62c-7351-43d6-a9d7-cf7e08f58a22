#include "Commands/UnrealMCPBlueprintNodeCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Engine/StaticMesh.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/SCS_Node.h"
#include "GameFramework/Actor.h"
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "K2Node_Event.h"
#include "K2Node_CallFunction.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "K2Node_InputAction.h"
#include "K2Node_Self.h"
#include "K2Node_CallArrayFunction.h"
#include "K2Node_ForEachElementInEnum.h"
#include "K2Node_MacroInstance.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "GameFramework/InputSettings.h"
#include "Camera/CameraActor.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetArrayLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "EdGraphSchema_K2.h"

// Declare the log category
DEFINE_LOG_CATEGORY_STATIC(LogUnrealMCP, Log, All);

FUnrealMCPBlueprintNodeCommands::FUnrealMCPBlueprintNodeCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleCommand(const FString &CommandType, const TSharedPtr<FJsonObject> &Params)
{
    if (CommandType == TEXT("connect_blueprint_nodes"))
    {
        return HandleConnectBlueprintNodes(Params);
    }
    else if (CommandType == TEXT("add_blueprint_get_self_component_reference"))
    {
        return HandleAddBlueprintGetSelfComponentReference(Params);
    }
    else if (CommandType == TEXT("add_blueprint_event_node"))
    {
        return HandleAddBlueprintEvent(Params);
    }
    else if (CommandType == TEXT("add_blueprint_function_node"))
    {
        return HandleAddBlueprintFunctionCall(Params);
    }
    else if (CommandType == TEXT("add_blueprint_variable"))
    {
        return HandleAddBlueprintVariable(Params);
    }
    else if (CommandType == TEXT("add_blueprint_input_action_node"))
    {
        return HandleAddBlueprintInputActionNode(Params);
    }
    else if (CommandType == TEXT("add_blueprint_self_reference"))
    {
        return HandleAddBlueprintSelfReference(Params);
    }
    else if (CommandType == TEXT("find_blueprint_nodes"))
    {
        return HandleFindBlueprintNodes(Params);
    }
    else if (CommandType == TEXT("add_blueprint_array_function"))
    {
        return HandleAddBlueprintArrayFunction(Params);
    }
    else if (CommandType == TEXT("add_blueprint_for_loop"))
    {
        return HandleAddBlueprintForLoop(Params);
    }
    else if (CommandType == TEXT("add_blueprint_for_each_loop"))
    {
        return HandleAddBlueprintForEachLoop(Params);
    }
    else if (CommandType == TEXT("add_blueprint_variable_get"))
    {
        return HandleAddBlueprintVariableGet(Params);
    }
    else if (CommandType == TEXT("add_blueprint_variable_set"))
    {
        return HandleAddBlueprintVariableSet(Params);
    }
    else if (CommandType == TEXT("add_blueprint_destroy_component"))
    {
        return HandleAddBlueprintDestroyComponent(Params);
    }
    else if (CommandType == TEXT("add_blueprint_array_get"))
    {
        return HandleAddBlueprintArrayGet(Params);
    }
    else if (CommandType == TEXT("add_blueprint_array_length"))
    {
        return HandleAddBlueprintArrayLength(Params);
    }
    else if (CommandType == TEXT("add_blueprint_array_clear"))
    {
        return HandleAddBlueprintArrayClear(Params);
    }
    // Spline-specific commands
    else if (CommandType == TEXT("add_blueprint_spline_get_number_of_points"))
    {
        return HandleAddBlueprintSplineGetNumberOfPoints(Params);
    }
    else if (CommandType == TEXT("add_blueprint_spline_get_location_at_point"))
    {
        return HandleAddBlueprintSplineGetLocationAtPoint(Params);
    }
    else if (CommandType == TEXT("add_blueprint_spline_get_tangent_at_point"))
    {
        return HandleAddBlueprintSplineGetTangentAtPoint(Params);
    }
    // Math operation commands
    else if (CommandType == TEXT("add_blueprint_math_operation"))
    {
        return HandleAddBlueprintMathOperation(Params);
    }
    else if (CommandType == TEXT("add_blueprint_branch_node"))
    {
        return HandleAddBlueprintBranchNode(Params);
    }
    // SplineMeshComponent commands
    else if (CommandType == TEXT("add_blueprint_add_spline_mesh_component"))
    {
        return HandleAddBlueprintAddSplineMeshComponent(Params);
    }
    else if (CommandType == TEXT("add_blueprint_spline_mesh_set_start_and_end"))
    {
        return HandleAddBlueprintSplineMeshSetStartAndEnd(Params);
    }
    else if (CommandType == TEXT("add_blueprint_spline_mesh_set_scale"))
    {
        return HandleAddBlueprintSplineMeshSetScale(Params);
    }
    else if (CommandType == TEXT("add_blueprint_set_mobility"))
    {
        return HandleAddBlueprintSetMobility(Params);
    }
    else if (CommandType == TEXT("add_blueprint_set_collision_enabled"))
    {
        return HandleAddBlueprintSetCollisionEnabled(Params);
    }
    else if (CommandType == TEXT("add_blueprint_set_collision_profile"))
    {
        return HandleAddBlueprintSetCollisionProfile(Params);
    }
    else if (CommandType == TEXT("add_blueprint_set_can_affect_navigation"))
    {
        return HandleAddBlueprintSetCanAffectNavigation(Params);
    }
    else if (CommandType == TEXT("add_blueprint_array_add"))
    {
        return HandleAddBlueprintArrayAdd(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown blueprint node command: %s"), *CommandType));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleConnectBlueprintNodes(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString SourceNodeId;
    if (!Params->TryGetStringField(TEXT("source_node_id"), SourceNodeId))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'source_node_id' parameter"));
    }

    FString TargetNodeId;
    if (!Params->TryGetStringField(TEXT("target_node_id"), TargetNodeId))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'target_node_id' parameter"));
    }

    FString SourcePinName;
    if (!Params->TryGetStringField(TEXT("source_pin"), SourcePinName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'source_pin' parameter"));
    }

    FString TargetPinName;
    if (!Params->TryGetStringField(TEXT("target_pin"), TargetPinName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'target_pin' parameter"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Find the nodes
    UEdGraphNode *SourceNode = nullptr;
    UEdGraphNode *TargetNode = nullptr;
    for (UEdGraphNode *Node : EventGraph->Nodes)
    {
        if (Node->NodeGuid.ToString() == SourceNodeId)
        {
            SourceNode = Node;
        }
        else if (Node->NodeGuid.ToString() == TargetNodeId)
        {
            TargetNode = Node;
        }
    }

    if (!SourceNode || !TargetNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Source or target node not found"));
    }

    // Connect the nodes
    if (FUnrealMCPCommonUtils::ConnectGraphNodes(EventGraph, SourceNode, SourcePinName, TargetNode, TargetPinName))
    {
        // Mark the blueprint as modified
        FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("source_node_id"), SourceNodeId);
        ResultObj->SetStringField(TEXT("target_node_id"), TargetNodeId);
        return ResultObj;
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to connect nodes"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintGetSelfComponentReference(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // We'll skip component verification since the GetAllNodes API may have changed in UE5.5

    // Create the variable get node directly
    UK2Node_VariableGet *GetComponentNode = NewObject<UK2Node_VariableGet>(EventGraph);
    if (!GetComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create get component node"));
    }

    // Set up the variable reference properly for UE5.5
    FMemberReference &VarRef = GetComponentNode->VariableReference;
    VarRef.SetSelfMember(FName(*ComponentName));

    // Set node position
    GetComponentNode->NodePosX = NodePosition.X;
    GetComponentNode->NodePosY = NodePosition.Y;

    // Add to graph
    EventGraph->AddNode(GetComponentNode);
    GetComponentNode->CreateNewGuid();
    GetComponentNode->PostPlacedNewNode();
    GetComponentNode->AllocateDefaultPins();

    // Explicitly reconstruct node for UE5.5
    GetComponentNode->ReconstructNode();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), GetComponentNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintEvent(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString EventName;
    if (!Params->TryGetStringField(TEXT("event_name"), EventName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'event_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Special handling for Construction Script
    if (EventName == TEXT("UserConstructionScript") || EventName == TEXT("ReceiveUserConstructionScript") || EventName == TEXT("Construction Script"))
    {
        // Find or create the Construction Script graph
        UEdGraph *ConstructionGraph = nullptr;
        for (UEdGraph *Graph : Blueprint->UbergraphPages)
        {
            if (Graph->GetFName() == "UserConstructionScript")
            {
                ConstructionGraph = Graph;
                break;
            }
        }

        if (!ConstructionGraph)
        {
            ConstructionGraph = FBlueprintEditorUtils::CreateNewGraph(Blueprint,
                                                                      FName(TEXT("UserConstructionScript")),
                                                                      UEdGraph::StaticClass(),
                                                                      UEdGraphSchema_K2::StaticClass());
            FBlueprintEditorUtils::AddUbergraphPage(Blueprint, ConstructionGraph);
        }

        // Check if Construction Script event node already exists
        UK2Node_Event *ExistingEventNode = nullptr;
        for (UEdGraphNode *Node : ConstructionGraph->Nodes)
        {
            UK2Node_Event *EventNode = Cast<UK2Node_Event>(Node);
            if (EventNode && EventNode->EventReference.GetMemberName() == FName(TEXT("UserConstructionScript")))
            {
                ExistingEventNode = EventNode;
                break;
            }
        }

        if (ExistingEventNode)
        {
            UE_LOG(LogTemp, Display, TEXT("Using existing Construction Script event node (ID: %s)"),
                   *ExistingEventNode->NodeGuid.ToString());
            
            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("node_id"), ExistingEventNode->NodeGuid.ToString());
            return ResultObj;
        }

        // Create the Construction Script event node
        UK2Node_Event *EventNode = NewObject<UK2Node_Event>(ConstructionGraph);
        EventNode->EventReference.SetSelfMember(FName(TEXT("UserConstructionScript")));
        EventNode->NodePosX = NodePosition.X;
        EventNode->NodePosY = NodePosition.Y;

        // Add to graph and generate GUID
        ConstructionGraph->AddNode(EventNode, true);
        EventNode->CreateNewGuid();
        EventNode->PostPlacedNewNode();
        EventNode->AllocateDefaultPins();

        // Mark the blueprint as modified
        FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

        UE_LOG(LogTemp, Display, TEXT("Created Construction Script event node (ID: %s)"),
               *EventNode->NodeGuid.ToString());

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("node_id"), EventNode->NodeGuid.ToString());
        return ResultObj;
    }

    // Get the event graph for regular events
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create the event node
    UK2Node_Event *EventNode = FUnrealMCPCommonUtils::CreateEventNode(EventGraph, EventName, NodePosition);
    if (!EventNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create event node"));
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), EventNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintFunctionCall(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString FunctionName;
    if (!Params->TryGetStringField(TEXT("function_name"), FunctionName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'function_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Check for target parameter (optional)
    FString Target;
    Params->TryGetStringField(TEXT("target"), Target);

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Find the function
    UFunction *Function = nullptr;
    UK2Node_CallFunction *FunctionNode = nullptr;

    // Add extensive logging for debugging
    UE_LOG(LogTemp, Display, TEXT("Looking for function '%s' in target '%s'"),
           *FunctionName, Target.IsEmpty() ? TEXT("Blueprint") : *Target);

    // Check if we have a target class specified
    if (!Target.IsEmpty())
    {
        // Try to find the target class
        UClass *TargetClass = nullptr;

        // Special handling for component references within the blueprint
        if (Blueprint->SimpleConstructionScript)
        {
            for (USCS_Node *Node : Blueprint->SimpleConstructionScript->GetAllNodes())
            {
                if (Node && Node->GetVariableName().ToString() == Target)
                {
                    UE_LOG(LogTemp, Display, TEXT("Found component '%s' in blueprint with class '%s'"),
                           *Target, Node->ComponentClass ? *Node->ComponentClass->GetName() : TEXT("NULL"));
                    TargetClass = Node->ComponentClass;

                    // Ensure we have the correct class loaded
                    if (TargetClass && TargetClass->GetName() == TEXT("SplineComponent"))
                    {
                        // Force load USplineComponent if needed
                        UClass *SplineClass = USplineComponent::StaticClass();
                        if (SplineClass)
                        {
                            TargetClass = SplineClass;
                            UE_LOG(LogTemp, Display, TEXT("Using USplineComponent::StaticClass() for target"));
                        }
                    }
                    break;
                }
            }
        }

        // If not found in components, try standard class resolution
        if (!TargetClass)
        {
            // First try without a prefix
            TargetClass = FindFirstObject<UClass>(*Target);
            UE_LOG(LogTemp, Display, TEXT("Tried to find class '%s': %s"),
                   *Target, TargetClass ? TEXT("Found") : TEXT("Not found"));

            // If not found, try with U prefix (common convention for UE classes)
            if (!TargetClass && !Target.StartsWith(TEXT("U")))
            {
                FString TargetWithPrefix = FString(TEXT("U")) + Target;
                TargetClass = FindFirstObject<UClass>(*TargetWithPrefix);
                UE_LOG(LogTemp, Display, TEXT("Tried to find class '%s': %s"),
                       *TargetWithPrefix, TargetClass ? TEXT("Found") : TEXT("Not found"));
            }

            // If still not found, try with common component names
            if (!TargetClass)
            {
                // Try some common component class names
                TArray<FString> PossibleClassNames;
                PossibleClassNames.Add(FString(TEXT("U")) + Target + TEXT("Component"));
                PossibleClassNames.Add(Target + TEXT("Component"));

                for (const FString &ClassName : PossibleClassNames)
                {
                    TargetClass = FindFirstObject<UClass>(*ClassName);
                    if (TargetClass)
                    {
                        UE_LOG(LogTemp, Display, TEXT("Found class using alternative name '%s'"), *ClassName);
                        break;
                    }
                }
            }
        }

        // Special case handling for common classes like UGameplayStatics
        if (!TargetClass && Target == TEXT("UGameplayStatics"))
        {
            // For UGameplayStatics, use a direct reference to known class
            TargetClass = FindFirstObject<UClass>(TEXT("UGameplayStatics"));
            if (!TargetClass)
            {
                // Try loading it from its known package
                TargetClass = LoadObject<UClass>(nullptr, TEXT("/Script/Engine.GameplayStatics"));
                UE_LOG(LogTemp, Display, TEXT("Explicitly loading GameplayStatics: %s"),
                       TargetClass ? TEXT("Success") : TEXT("Failed"));
            }
        }

        // If we found a target class, look for the function there
        if (TargetClass)
        {
            UE_LOG(LogTemp, Display, TEXT("Looking for function '%s' in class '%s'"),
                   *FunctionName, *TargetClass->GetName());

            // First try exact name
            Function = TargetClass->FindFunctionByName(*FunctionName);

            // If not found, try class hierarchy
            UClass *CurrentClass = TargetClass;
            while (!Function && CurrentClass)
            {
                UE_LOG(LogTemp, Display, TEXT("Searching in class: %s"), *CurrentClass->GetName());

                // Try exact match
                Function = CurrentClass->FindFunctionByName(*FunctionName);

                // Try case-insensitive match
                if (!Function)
                {
                    for (TFieldIterator<UFunction> FuncIt(CurrentClass); FuncIt; ++FuncIt)
                    {
                        UFunction *AvailableFunc = *FuncIt;
                        UE_LOG(LogTemp, Display, TEXT("  - Available function: %s"), *AvailableFunc->GetName());

                        if (AvailableFunc->GetName().Equals(FunctionName, ESearchCase::IgnoreCase))
                        {
                            UE_LOG(LogTemp, Display, TEXT("  - Found case-insensitive match: %s"), *AvailableFunc->GetName());
                            Function = AvailableFunc;
                            break;
                        }
                    }
                }

                // Move to parent class
                CurrentClass = CurrentClass->GetSuperClass();
            }

            // Special handling for known functions
            if (!Function)
            {
                if (TargetClass->GetName() == TEXT("GameplayStatics") &&
                    (FunctionName == TEXT("GetActorOfClass") || FunctionName.Equals(TEXT("GetActorOfClass"), ESearchCase::IgnoreCase)))
                {
                    UE_LOG(LogTemp, Display, TEXT("Using special case handling for GameplayStatics::GetActorOfClass"));

                    // Create the function node directly
                    FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
                    if (FunctionNode)
                    {
                        // Direct setup for known function
                        FunctionNode->FunctionReference.SetExternalMember(
                            FName(TEXT("GetActorOfClass")),
                            TargetClass);

                        FunctionNode->NodePosX = NodePosition.X;
                        FunctionNode->NodePosY = NodePosition.Y;
                        EventGraph->AddNode(FunctionNode);
                        FunctionNode->CreateNewGuid();
                        FunctionNode->PostPlacedNewNode();
                        FunctionNode->AllocateDefaultPins();

                        UE_LOG(LogTemp, Display, TEXT("Created GetActorOfClass node directly"));

                        // List all pins
                        for (UEdGraphPin *Pin : FunctionNode->Pins)
                        {
                            UE_LOG(LogTemp, Display, TEXT("  - Pin: %s, Direction: %d, Category: %s"),
                                   *Pin->PinName.ToString(), (int32)Pin->Direction, *Pin->PinType.PinCategory.ToString());
                        }
                    }
                }
            }
        }
    }

    // If we still haven't found the function, try in the blueprint's class
    if (!Function && !FunctionNode)
    {
        UE_LOG(LogTemp, Display, TEXT("Trying to find function in blueprint class"));
        Function = Blueprint->GeneratedClass->FindFunctionByName(*FunctionName);
    }

    // Create the function call node if we found the function
    if (Function && !FunctionNode)
    {
        FunctionNode = FUnrealMCPCommonUtils::CreateFunctionCallNode(EventGraph, Function, NodePosition);
    }

    if (!FunctionNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Function not found: %s in target %s"), *FunctionName, Target.IsEmpty() ? TEXT("Blueprint") : *Target));
    }

    // Set parameters if provided
    if (Params->HasField(TEXT("params")))
    {
        const TSharedPtr<FJsonObject> *ParamsObj;
        if (Params->TryGetObjectField(TEXT("params"), ParamsObj))
        {
            // Process parameters
            for (const TPair<FString, TSharedPtr<FJsonValue>> &Param : (*ParamsObj)->Values)
            {
                const FString &ParamName = Param.Key;
                const TSharedPtr<FJsonValue> &ParamValue = Param.Value;

                // Find the parameter pin
                UEdGraphPin *ParamPin = FUnrealMCPCommonUtils::FindPin(FunctionNode, ParamName, EGPD_Input);
                if (ParamPin)
                {
                    UE_LOG(LogTemp, Display, TEXT("Found parameter pin '%s' of category '%s'"),
                           *ParamName, *ParamPin->PinType.PinCategory.ToString());
                    UE_LOG(LogTemp, Display, TEXT("  Current default value: '%s'"), *ParamPin->DefaultValue);
                    if (ParamPin->PinType.PinSubCategoryObject.IsValid())
                    {
                        UE_LOG(LogTemp, Display, TEXT("  Pin subcategory: '%s'"),
                               *ParamPin->PinType.PinSubCategoryObject->GetName());
                    }

                    // Set parameter based on type
                    if (ParamValue->Type == EJson::String)
                    {
                        FString StringVal = ParamValue->AsString();
                        UE_LOG(LogTemp, Display, TEXT("  Setting string parameter '%s' to: '%s'"),
                               *ParamName, *StringVal);

                        // Handle class reference parameters (e.g., ActorClass in GetActorOfClass)
                        if (ParamPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Class)
                        {
                            // For class references, we require the exact class name with proper prefix
                            // - Actor classes must start with 'A' (e.g., ACameraActor)
                            // - Non-actor classes must start with 'U' (e.g., UObject)
                            const FString &ClassName = StringVal;

                            // TODO: This likely won't work in UE5.5+, so don't rely on it.
                            UClass *Class = FindFirstObject<UClass>(*ClassName);

                            if (!Class)
                            {
                                Class = LoadObject<UClass>(nullptr, *ClassName);
                                UE_LOG(LogUnrealMCP, Display, TEXT("FindObject<UClass> failed. Assuming soft path  path: %s"), *ClassName);
                            }

                            // If not found, try with Engine module path
                            if (!Class)
                            {
                                FString EngineClassName = FString::Printf(TEXT("/Script/Engine.%s"), *ClassName);
                                Class = LoadObject<UClass>(nullptr, *EngineClassName);
                                UE_LOG(LogUnrealMCP, Display, TEXT("Trying Engine module path: %s"), *EngineClassName);
                            }

                            if (!Class)
                            {
                                UE_LOG(LogUnrealMCP, Error, TEXT("Failed to find class '%s'. Make sure to use the exact class name with proper prefix (A for actors, U for non-actors)"), *ClassName);
                                return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to find class '%s'"), *ClassName));
                            }

                            const UEdGraphSchema_K2 *K2Schema = Cast<const UEdGraphSchema_K2>(EventGraph->GetSchema());
                            if (!K2Schema)
                            {
                                UE_LOG(LogUnrealMCP, Error, TEXT("Failed to get K2Schema"));
                                return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get K2Schema"));
                            }

                            K2Schema->TrySetDefaultObject(*ParamPin, Class);
                            if (ParamPin->DefaultObject != Class)
                            {
                                UE_LOG(LogUnrealMCP, Error, TEXT("Failed to set class reference for pin '%s' to '%s'"), *ParamPin->PinName.ToString(), *ClassName);
                                return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to set class reference for pin '%s'"), *ParamPin->PinName.ToString()));
                            }

                            UE_LOG(LogUnrealMCP, Log, TEXT("Successfully set class reference for pin '%s' to '%s'"), *ParamPin->PinName.ToString(), *ClassName);
                            continue;
                        }
                        else if (ParamPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Int)
                        {
                            // Ensure we're using an integer value (no decimal)
                            int32 IntValue = FMath::RoundToInt(ParamValue->AsNumber());
                            ParamPin->DefaultValue = FString::FromInt(IntValue);
                            UE_LOG(LogTemp, Display, TEXT("  Set integer parameter '%s' to: %d (string: '%s')"),
                                   *ParamName, IntValue, *ParamPin->DefaultValue);
                        }
                        else if (ParamPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Float)
                        {
                            // For other numeric types
                            float FloatValue = ParamValue->AsNumber();
                            ParamPin->DefaultValue = FString::SanitizeFloat(FloatValue);
                            UE_LOG(LogTemp, Display, TEXT("  Set float parameter '%s' to: %f (string: '%s')"),
                                   *ParamName, FloatValue, *ParamPin->DefaultValue);
                        }
                        else if (ParamPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Boolean)
                        {
                            bool BoolValue = ParamValue->AsBool();
                            ParamPin->DefaultValue = BoolValue ? TEXT("true") : TEXT("false");
                            UE_LOG(LogTemp, Display, TEXT("  Set boolean parameter '%s' to: %s"),
                                   *ParamName, *ParamPin->DefaultValue);
                        }
                        else if (ParamPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Struct && ParamPin->PinType.PinSubCategoryObject == TBaseStructure<FVector>::Get())
                        {
                            // Handle array parameters - like Vector parameters
                            const TArray<TSharedPtr<FJsonValue>> *ArrayValue;
                            if (ParamValue->TryGetArray(ArrayValue))
                            {
                                // Check if this could be a vector (array of 3 numbers)
                                if (ArrayValue->Num() == 3)
                                {
                                    // Create a proper vector string: (X=0.0,Y=0.0,Z=1000.0)
                                    float X = (*ArrayValue)[0]->AsNumber();
                                    float Y = (*ArrayValue)[1]->AsNumber();
                                    float Z = (*ArrayValue)[2]->AsNumber();

                                    FString VectorString = FString::Printf(TEXT("(X=%f,Y=%f,Z=%f)"), X, Y, Z);
                                    ParamPin->DefaultValue = VectorString;

                                    UE_LOG(LogTemp, Display, TEXT("  Set vector parameter '%s' to: %s"),
                                           *ParamName, *VectorString);
                                    UE_LOG(LogTemp, Display, TEXT("  Final pin value: '%s'"),
                                           *ParamPin->DefaultValue);
                                }
                                else
                                {
                                    UE_LOG(LogTemp, Warning, TEXT("Array parameter type not fully supported yet"));
                                }
                            }
                        }
                    }
                    else if (ParamValue->Type == EJson::Number)
                    {
                        // Handle integer vs float parameters correctly
                        if (ParamPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Int)
                        {
                            // Ensure we're using an integer value (no decimal)
                            int32 IntValue = FMath::RoundToInt(ParamValue->AsNumber());
                            ParamPin->DefaultValue = FString::FromInt(IntValue);
                            UE_LOG(LogTemp, Display, TEXT("  Set integer parameter '%s' to: %d (string: '%s')"),
                                   *ParamName, IntValue, *ParamPin->DefaultValue);
                        }
                        else
                        {
                            // For other numeric types
                            float FloatValue = ParamValue->AsNumber();
                            ParamPin->DefaultValue = FString::SanitizeFloat(FloatValue);
                            UE_LOG(LogTemp, Display, TEXT("  Set float parameter '%s' to: %f (string: '%s')"),
                                   *ParamName, FloatValue, *ParamPin->DefaultValue);
                        }
                    }
                    else if (ParamValue->Type == EJson::Boolean)
                    {
                        bool BoolValue = ParamValue->AsBool();
                        ParamPin->DefaultValue = BoolValue ? TEXT("true") : TEXT("false");
                        UE_LOG(LogTemp, Display, TEXT("  Set boolean parameter '%s' to: %s"),
                               *ParamName, *ParamPin->DefaultValue);
                    }
                    else if (ParamValue->Type == EJson::Array)
                    {
                        UE_LOG(LogTemp, Display, TEXT("  Processing array parameter '%s'"), *ParamName);
                        // Handle array parameters - like Vector parameters
                        const TArray<TSharedPtr<FJsonValue>> *ArrayValue;
                        if (ParamValue->TryGetArray(ArrayValue))
                        {
                            // Check if this could be a vector (array of 3 numbers)
                            if (ArrayValue->Num() == 3 &&
                                (ParamPin->PinType.PinCategory == UEdGraphSchema_K2::PC_Struct) &&
                                (ParamPin->PinType.PinSubCategoryObject == TBaseStructure<FVector>::Get()))
                            {
                                // Create a proper vector string: (X=0.0,Y=0.0,Z=1000.0)
                                float X = (*ArrayValue)[0]->AsNumber();
                                float Y = (*ArrayValue)[1]->AsNumber();
                                float Z = (*ArrayValue)[2]->AsNumber();

                                FString VectorString = FString::Printf(TEXT("(X=%f,Y=%f,Z=%f)"), X, Y, Z);
                                ParamPin->DefaultValue = VectorString;

                                UE_LOG(LogTemp, Display, TEXT("  Set vector parameter '%s' to: %s"),
                                       *ParamName, *VectorString);
                                UE_LOG(LogTemp, Display, TEXT("  Final pin value: '%s'"),
                                       *ParamPin->DefaultValue);
                            }
                            else
                            {
                                UE_LOG(LogTemp, Warning, TEXT("Array parameter type not fully supported yet"));
                            }
                        }
                    }
                    // Add handling for other types as needed
                }
                else
                {
                    UE_LOG(LogTemp, Warning, TEXT("Parameter pin '%s' not found"), *ParamName);
                }
            }
        }
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), FunctionNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintVariable(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString VariableName;
    if (!Params->TryGetStringField(TEXT("variable_name"), VariableName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'variable_name' parameter"));
    }

    FString VariableType;
    if (!Params->TryGetStringField(TEXT("variable_type"), VariableType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'variable_type' parameter"));
    }

    // Get optional parameters
    bool IsExposed = false;
    if (Params->HasField(TEXT("is_exposed")))
    {
        IsExposed = Params->GetBoolField(TEXT("is_exposed"));
    }

    bool IsInstanceEditable = false;
    if (Params->HasField(TEXT("instance_editable")))
    {
        IsInstanceEditable = Params->GetBoolField(TEXT("instance_editable"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Create variable based on type
    FEdGraphPinType PinType;

    // Set up pin type based on variable_type string
    if (VariableType == TEXT("Boolean"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Boolean;
    }
    else if (VariableType == TEXT("Integer") || VariableType == TEXT("Int"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Int;
    }
    else if (VariableType == TEXT("Float"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Float;
    }
    else if (VariableType == TEXT("String"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_String;
    }
    else if (VariableType == TEXT("Vector"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
        PinType.PinSubCategoryObject = TBaseStructure<FVector>::Get();
    }
    else if (VariableType == TEXT("Vector2D"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
        PinType.PinSubCategoryObject = TBaseStructure<FVector2D>::Get();
    }
    else if (VariableType == TEXT("Name"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Name;
    }
    else if (VariableType == TEXT("Rotator"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
        PinType.PinSubCategoryObject = TBaseStructure<FRotator>::Get();
    }
    else if (VariableType == TEXT("Transform"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
        PinType.PinSubCategoryObject = TBaseStructure<FTransform>::Get();
    }
    else if (VariableType == TEXT("StaticMesh"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = UStaticMesh::StaticClass();
    }
    else if (VariableType == TEXT("Object"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = UObject::StaticClass();
    }
    else if (VariableType == TEXT("Actor"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = AActor::StaticClass();
    }
    else if (VariableType == TEXT("StaticMeshComponent"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = UStaticMeshComponent::StaticClass();
    }
    else if (VariableType == TEXT("SplineMeshComponent"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = USplineMeshComponent::StaticClass();
    }
    else if (VariableType == TEXT("Array"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = UObject::StaticClass();
        PinType.ContainerType = EPinContainerType::Array;
    }
    else if (VariableType == TEXT("StaticMeshArray"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = UStaticMeshComponent::StaticClass();
        PinType.ContainerType = EPinContainerType::Array;
    }
    else if (VariableType == TEXT("SplineMeshComponent Array") || VariableType == TEXT("SplineMeshComponentArray"))
    {
        PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
        PinType.PinSubCategoryObject = USplineMeshComponent::StaticClass();
        PinType.ContainerType = EPinContainerType::Array;
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unsupported variable type: %s"), *VariableType));
    }

    // Create the variable
    FBlueprintEditorUtils::AddMemberVariable(Blueprint, FName(*VariableName), PinType);

    // Set variable properties
    FBPVariableDescription *NewVar = nullptr;
    for (FBPVariableDescription &Variable : Blueprint->NewVariables)
    {
        if (Variable.VarName == FName(*VariableName))
        {
            NewVar = &Variable;
            break;
        }
    }

    if (NewVar)
    {
        // Set exposure in editor
        if (IsExposed)
        {
            NewVar->PropertyFlags |= CPF_Edit;
        }

        // Set instance editable
        if (IsInstanceEditable)
        {
            NewVar->PropertyFlags |= CPF_DisableEditOnInstance;
            NewVar->PropertyFlags &= ~CPF_DisableEditOnInstance; // Remove the disable flag
            NewVar->PropertyFlags |= CPF_Edit; // Ensure it's editable
        }
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("variable_name"), VariableName);
    ResultObj->SetStringField(TEXT("variable_type"), VariableType);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintInputActionNode(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ActionName;
    if (!Params->TryGetStringField(TEXT("action_name"), ActionName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'action_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create the input action node
    UK2Node_InputAction *InputActionNode = FUnrealMCPCommonUtils::CreateInputActionNode(EventGraph, ActionName, NodePosition);
    if (!InputActionNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create input action node"));
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), InputActionNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSelfReference(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create the self node
    UK2Node_Self *SelfNode = FUnrealMCPCommonUtils::CreateSelfReferenceNode(EventGraph, NodePosition);
    if (!SelfNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create self node"));
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), SelfNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleFindBlueprintNodes(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Optional parameters
    FString NodeType;
    Params->TryGetStringField(TEXT("node_type"), NodeType);

    FString EventType;
    Params->TryGetStringField(TEXT("event_type"), EventType);

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Create a JSON array for the node GUIDs
    TArray<TSharedPtr<FJsonValue>> NodeGuidArray;

    // Check for Construction Script event type
    if (EventType == TEXT("ConstructionScript"))
    {
        // Look in all graphs for Construction Script event
        for (UEdGraph *Graph : Blueprint->UbergraphPages)
        {
            if (Graph && Graph->GetFName() == "ConstructionScript")
            {
                for (UEdGraphNode *Node : Graph->Nodes)
                {
                    UK2Node_Event *EventNode = Cast<UK2Node_Event>(Node);
                    if (EventNode && EventNode->EventReference.GetMemberName() == FName(TEXT("UserConstructionScript")))
                    {
                        UE_LOG(LogTemp, Display, TEXT("Found Construction Script event node: %s"), *EventNode->NodeGuid.ToString());
                        NodeGuidArray.Add(MakeShared<FJsonValueString>(EventNode->NodeGuid.ToString()));
                    }
                }
            }
        }
    }
    else if (NodeType == TEXT("Event"))
    {
        FString EventName;
        if (!Params->TryGetStringField(TEXT("event_name"), EventName))
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'event_name' parameter for Event node search"));
        }

        // Get the event graph
        UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
        if (!EventGraph)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
        }

        // Look for nodes with exact event name (e.g., ReceiveBeginPlay)
        for (UEdGraphNode *Node : EventGraph->Nodes)
        {
            UK2Node_Event *EventNode = Cast<UK2Node_Event>(Node);
            if (EventNode && EventNode->EventReference.GetMemberName() == FName(*EventName))
            {
                UE_LOG(LogTemp, Display, TEXT("Found event node with name %s: %s"), *EventName, *EventNode->NodeGuid.ToString());
                NodeGuidArray.Add(MakeShared<FJsonValueString>(EventNode->NodeGuid.ToString()));
            }
        }
    }
    else if (NodeType.IsEmpty() && EventType.IsEmpty())
    {
        // Return all nodes if no filter specified
        UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
        if (EventGraph)
        {
            for (UEdGraphNode *Node : EventGraph->Nodes)
            {
                NodeGuidArray.Add(MakeShared<FJsonValueString>(Node->NodeGuid.ToString()));
            }
        }
    }

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetArrayField(TEXT("node_guids"), NodeGuidArray);

    return FUnrealMCPCommonUtils::CreateSuccessResponse(ResultObj);
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintArrayFunction(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString FunctionName;
    if (!Params->TryGetStringField(TEXT("function_name"), FunctionName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'function_name' parameter"));
    }

    FString ArrayVariableName;
    if (!Params->TryGetStringField(TEXT("array_variable"), ArrayVariableName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'array_variable' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create array function node
    UK2Node_CallArrayFunction *ArrayFunctionNode = NewObject<UK2Node_CallArrayFunction>(EventGraph);
    if (!ArrayFunctionNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create array function node"));
    }

    // Set the function name (Clear, Length, Get, Add, etc.)
    UFunction *ArrayFunction = nullptr;
    if (FunctionName == TEXT("Clear"))
    {
        ArrayFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_Clear"));
    }
    else if (FunctionName == TEXT("Length"))
    {
        ArrayFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_Length"));
    }
    else if (FunctionName == TEXT("Get"))
    {
        ArrayFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_Get"));
    }
    else if (FunctionName == TEXT("Add"))
    {
        ArrayFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_Add"));
    }
    else if (FunctionName == TEXT("AddUnique"))
    {
        ArrayFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_AddUnique"));
    }
    else if (FunctionName == TEXT("Remove"))
    {
        ArrayFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_Remove"));
    }
    else if (FunctionName == TEXT("RemoveItem"))
    {
        ArrayFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_RemoveItem"));
    }

    if (!ArrayFunction)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Array function not found: %s"), *FunctionName));
    }

    ArrayFunctionNode->SetFromFunction(ArrayFunction);
    ArrayFunctionNode->NodePosX = NodePosition.X;
    ArrayFunctionNode->NodePosY = NodePosition.Y;

    // Add to graph
    EventGraph->AddNode(ArrayFunctionNode, true, true);

    // Allocate default pins
    ArrayFunctionNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), ArrayFunctionNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintForLoop(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create a custom For Loop node using UK2Node_CallFunction with proper For Loop implementation
    UK2Node_CallFunction *ForLoopNode = NewObject<UK2Node_CallFunction>(EventGraph);
    if (!ForLoopNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create for loop node"));
    }

    // Find the For Loop function from KismetSystemLibrary
    UFunction *ForLoopFunction = nullptr;

    // Try to find the For Loop With Break function which is commonly used
    ForLoopFunction = UKismetSystemLibrary::StaticClass()->FindFunctionByName(TEXT("ForLoopWithBreak"));

    if (!ForLoopFunction)
    {
        // Fallback to regular For Loop if available
        ForLoopFunction = UKismetSystemLibrary::StaticClass()->FindFunctionByName(TEXT("ForLoop"));
    }

    if (!ForLoopFunction)
    {
        // Create a macro instance approach as final fallback
        UK2Node_MacroInstance *MacroForLoopNode = NewObject<UK2Node_MacroInstance>(EventGraph);
        if (!MacroForLoopNode)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create macro for loop node"));
        }

        // Try to find the For Loop macro from the engine's standard macros
        UBlueprint *StandardMacros = LoadObject<UBlueprint>(nullptr, TEXT("/Engine/EditorBlueprintResources/StandardMacros.StandardMacros"));
        if (StandardMacros)
        {
            UEdGraph *ForLoopMacro = nullptr;
            for (UEdGraph *MacroGraph : StandardMacros->MacroGraphs)
            {
                if (MacroGraph && (MacroGraph->GetFName() == TEXT("ForLoop") || MacroGraph->GetFName().ToString().Contains(TEXT("For Loop"))))
                {
                    ForLoopMacro = MacroGraph;
                    break;
                }
            }

            if (ForLoopMacro)
            {
                MacroForLoopNode->SetMacroGraph(ForLoopMacro);
                MacroForLoopNode->NodePosX = NodePosition.X;
                MacroForLoopNode->NodePosY = NodePosition.Y;
                MacroForLoopNode->NodeGuid = FGuid::NewGuid();

                EventGraph->AddNode(MacroForLoopNode, true, true);
                MacroForLoopNode->AllocateDefaultPins();

                FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

                TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
                ResultObj->SetStringField(TEXT("node_id"), MacroForLoopNode->NodeGuid.ToString());
                ResultObj->SetStringField(TEXT("node_type"), TEXT("ForLoopMacro"));
                return ResultObj;
            }
        }

        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find For Loop macro or function"));
    }

    // Set up the function call node
    ForLoopNode->SetFromFunction(ForLoopFunction);
    ForLoopNode->NodePosX = NodePosition.X;
    ForLoopNode->NodePosY = NodePosition.Y;
    ForLoopNode->NodeGuid = FGuid::NewGuid();

    // Add to graph
    EventGraph->AddNode(ForLoopNode, true, true);

    // Allocate default pins
    ForLoopNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), ForLoopNode->NodeGuid.ToString());
    ResultObj->SetStringField(TEXT("node_type"), TEXT("ForLoop"));
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintForEachLoop(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ArrayType = TEXT("Object");
    if (Params->HasField(TEXT("array_type")))
    {
        Params->TryGetStringField(TEXT("array_type"), ArrayType);
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Try to create a macro instance approach for ForEach loop
    UK2Node_MacroInstance *MacroForEachLoopNode = NewObject<UK2Node_MacroInstance>(EventGraph);
    if (!MacroForEachLoopNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create macro for each loop node"));
    }

    // Try to find the ForEach Loop macro from the engine's standard macros
    UBlueprint *StandardMacros = LoadObject<UBlueprint>(nullptr, TEXT("/Engine/EditorBlueprintResources/StandardMacros.StandardMacros"));
    if (StandardMacros)
    {
        UEdGraph *ForEachLoopMacro = nullptr;
        for (UEdGraph *MacroGraph : StandardMacros->MacroGraphs)
        {
            if (MacroGraph && (MacroGraph->GetFName() == TEXT("ForEachLoop") ||
                               MacroGraph->GetFName().ToString().Contains(TEXT("For Each Loop")) ||
                               MacroGraph->GetFName().ToString().Contains(TEXT("ForEach"))))
            {
                ForEachLoopMacro = MacroGraph;
                break;
            }
        }

        if (ForEachLoopMacro)
        {
            MacroForEachLoopNode->SetMacroGraph(ForEachLoopMacro);
            MacroForEachLoopNode->NodePosX = NodePosition.X;
            MacroForEachLoopNode->NodePosY = NodePosition.Y;
            MacroForEachLoopNode->NodeGuid = FGuid::NewGuid();

            EventGraph->AddNode(MacroForEachLoopNode, true, true);
            MacroForEachLoopNode->AllocateDefaultPins();

            FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("node_id"), MacroForEachLoopNode->NodeGuid.ToString());
            ResultObj->SetStringField(TEXT("node_type"), TEXT("ForEachLoopMacro"));
            return ResultObj;
        }
    }

    // Fallback: Create a simple array function node that can be used for iteration
    UK2Node_CallArrayFunction *ForEachLoopNode = NewObject<UK2Node_CallArrayFunction>(EventGraph);
    if (!ForEachLoopNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create for each loop node"));
    }

    // Use Array_Length as a simple fallback that can be connected to a For Loop
    UFunction *ArrayLengthFunction = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_Length"));
    if (!ArrayLengthFunction)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No suitable array function found for ForEach implementation"));
    }

    ForEachLoopNode->SetFromFunction(ArrayLengthFunction);
    ForEachLoopNode->NodePosX = NodePosition.X;
    ForEachLoopNode->NodePosY = NodePosition.Y;

    // Generate a new GUID for the node
    ForEachLoopNode->NodeGuid = FGuid::NewGuid();

    // Add to graph
    EventGraph->AddNode(ForEachLoopNode, true, true);

    // Allocate default pins
    ForEachLoopNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), ForEachLoopNode->NodeGuid.ToString());
    ResultObj->SetBoolField(TEXT("success"), true);
    ResultObj->SetStringField(TEXT("message"), TEXT("ForEach loop node created using Array_Length fallback"));
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintVariableGet(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString VariableName;
    if (!Params->TryGetStringField(TEXT("variable_name"), VariableName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'variable_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create variable get node using the common utils function
    UK2Node_VariableGet *VariableGetNode = FUnrealMCPCommonUtils::CreateVariableGetNode(EventGraph, Blueprint, VariableName, NodePosition);
    if (!VariableGetNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to create variable get node for: %s"), *VariableName));
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), VariableGetNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintVariableSet(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString VariableName;
    if (!Params->TryGetStringField(TEXT("variable_name"), VariableName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'variable_name' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create variable set node
    UK2Node_VariableSet *VariableSetNode = NewObject<UK2Node_VariableSet>(EventGraph);
    if (!VariableSetNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create variable set node"));
    }

    // Find the variable in the blueprint
    FBPVariableDescription *FoundVariable = nullptr;
    for (FBPVariableDescription &Variable : Blueprint->NewVariables)
    {
        if (Variable.VarName == FName(*VariableName))
        {
            FoundVariable = &Variable;
            break;
        }
    }

    if (!FoundVariable)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Variable not found: %s"), *VariableName));
    }

    // Set up the variable set node
    VariableSetNode->VariableReference.SetSelfMember(FName(*VariableName));
    VariableSetNode->NodePosX = NodePosition.X;
    VariableSetNode->NodePosY = NodePosition.Y;

    // Add to graph
    EventGraph->AddNode(VariableSetNode, true, true);

    // Allocate default pins
    VariableSetNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), VariableSetNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintDestroyComponent(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentReference;
    if (!Params->TryGetStringField(TEXT("component_reference"), ComponentReference))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_reference' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create a function call node for DestroyComponent
    UK2Node_CallFunction *DestroyComponentNode = NewObject<UK2Node_CallFunction>(EventGraph);
    DestroyComponentNode->FunctionReference.SetExternalMember(GET_FUNCTION_NAME_CHECKED(UActorComponent, DestroyComponent), UActorComponent::StaticClass());
    DestroyComponentNode->NodePosX = NodePosition.X;
    DestroyComponentNode->NodePosY = NodePosition.Y;

    // Generate a new GUID for the node
    DestroyComponentNode->NodeGuid = FGuid::NewGuid();

    // Add to graph
    EventGraph->AddNode(DestroyComponentNode, true, true);

    // Allocate default pins
    DestroyComponentNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), DestroyComponentNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintArrayGet(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ArrayVariable;
    if (!Params->TryGetStringField(TEXT("array_variable"), ArrayVariable))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'array_variable' parameter"));
    }

    int32 Index = 0;
    if (Params->HasField(TEXT("index")))
    {
        Index = Params->GetIntegerField(TEXT("index"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create an Array Get node
    UK2Node_CallArrayFunction *ArrayGetNode = NewObject<UK2Node_CallArrayFunction>(EventGraph);
    ArrayGetNode->FunctionReference.SetExternalMember(GET_FUNCTION_NAME_CHECKED(UKismetArrayLibrary, Array_Get), UKismetArrayLibrary::StaticClass());
    ArrayGetNode->NodePosX = NodePosition.X;
    ArrayGetNode->NodePosY = NodePosition.Y;

    // Generate a new GUID for the node
    ArrayGetNode->NodeGuid = FGuid::NewGuid();

    // Add to graph
    EventGraph->AddNode(ArrayGetNode, true, true);

    // Allocate default pins
    ArrayGetNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), ArrayGetNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintArrayLength(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ArrayVariable;
    if (!Params->TryGetStringField(TEXT("array_variable"), ArrayVariable))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'array_variable' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create an Array Length node
    UK2Node_CallArrayFunction *ArrayLengthNode = NewObject<UK2Node_CallArrayFunction>(EventGraph);
    ArrayLengthNode->FunctionReference.SetExternalMember(GET_FUNCTION_NAME_CHECKED(UKismetArrayLibrary, Array_Length), UKismetArrayLibrary::StaticClass());
    ArrayLengthNode->NodePosX = NodePosition.X;
    ArrayLengthNode->NodePosY = NodePosition.Y;

    // Generate a new GUID for the node
    ArrayLengthNode->NodeGuid = FGuid::NewGuid();

    // Add to graph
    EventGraph->AddNode(ArrayLengthNode, true, true);

    // Allocate default pins
    ArrayLengthNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), ArrayLengthNode->NodeGuid.ToString());
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintArrayClear(const TSharedPtr<FJsonObject> &Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ArrayVariable;
    if (!Params->TryGetStringField(TEXT("array_variable"), ArrayVariable))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'array_variable' parameter"));
    }

    // Get position parameters (optional)
    FVector2D NodePosition(0.0f, 0.0f);
    if (Params->HasField(TEXT("node_position")))
    {
        NodePosition = FUnrealMCPCommonUtils::GetVector2DFromJson(Params, TEXT("node_position"));
    }

    // Find the blueprint
    UBlueprint *Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph *EventGraph = FUnrealMCPCommonUtils::FindOrCreateEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get event graph"));
    }

    // Create an Array Clear node
    UK2Node_CallArrayFunction *ArrayClearNode = NewObject<UK2Node_CallArrayFunction>(EventGraph);
    ArrayClearNode->FunctionReference.SetExternalMember(GET_FUNCTION_NAME_CHECKED(UKismetArrayLibrary, Array_Clear), UKismetArrayLibrary::StaticClass());
    ArrayClearNode->NodePosX = NodePosition.X;
    ArrayClearNode->NodePosY = NodePosition.Y;

    // Generate a new GUID for the node
    ArrayClearNode->NodeGuid = FGuid::NewGuid();

    // Add to graph
    EventGraph->AddNode(ArrayClearNode, true, true);

    // Allocate default pins
    ArrayClearNode->AllocateDefaultPins();

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("node_id"), ArrayClearNode->NodeGuid.ToString());
    return ResultObj;
}

// Spline-specific node implementations
TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSplineGetNumberOfPoints(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString SplineComponentName;
    if (!Params->TryGetStringField(TEXT("spline_component"), SplineComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'spline_component' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    // Create a function call node for GetNumberOfSplinePoints
    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = USplineComponent::StaticClass()->FindFunctionByName(TEXT("GetNumberOfSplinePoints"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("GetNumberOfSplinePoints function not found"));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(TEXT("Spline GetNumberOfSplinePoints node added successfully"), NodeId);
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSplineGetLocationAtPoint(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString SplineComponentName;
    if (!Params->TryGetStringField(TEXT("spline_component"), SplineComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'spline_component' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    // Create a function call node for GetLocationAtSplinePoint
    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = USplineComponent::StaticClass()->FindFunctionByName(TEXT("GetLocationAtSplinePoint"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("GetLocationAtSplinePoint function not found"));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Set default coordinate space to Local if specified
    FString CoordinateSpace;
    if (Params->TryGetStringField(TEXT("coordinate_space"), CoordinateSpace))
    {
        UEdGraphPin* CoordinateSpacePin = FunctionNode->FindPinChecked(TEXT("CoordinateSpace"));
        if (CoordinateSpacePin && CoordinateSpace == TEXT("Local"))
        {
            CoordinateSpacePin->DefaultValue = TEXT("Local");
        }
    }

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(TEXT("Spline GetLocationAtSplinePoint node added successfully"), NodeId);
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSplineGetTangentAtPoint(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString SplineComponentName;
    if (!Params->TryGetStringField(TEXT("spline_component"), SplineComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'spline_component' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    // Create a function call node for GetTangentAtSplinePoint
    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = USplineComponent::StaticClass()->FindFunctionByName(TEXT("GetTangentAtSplinePoint"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("GetTangentAtSplinePoint function not found"));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Set default coordinate space to Local if specified
    FString CoordinateSpace;
    if (Params->TryGetStringField(TEXT("coordinate_space"), CoordinateSpace))
    {
        UEdGraphPin* CoordinateSpacePin = FunctionNode->FindPinChecked(TEXT("CoordinateSpace"));
        if (CoordinateSpacePin && CoordinateSpace == TEXT("Local"))
        {
            CoordinateSpacePin->DefaultValue = TEXT("Local");
        }
    }

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(TEXT("Spline GetTangentAtSplinePoint node added successfully"), NodeId);
}

// Math operation node implementations
TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintMathOperation(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString Operation;
    if (!Params->TryGetStringField(TEXT("operation"), Operation))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'operation' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = nullptr;

    // Determine which math function to use based on operation
    if (Operation == TEXT("subtract_int"))
    {
        Function = UKismetMathLibrary::StaticClass()->FindFunctionByName(TEXT("Subtract_IntInt"));
    }
    else if (Operation == TEXT("add_int"))
    {
        Function = UKismetMathLibrary::StaticClass()->FindFunctionByName(TEXT("Add_IntInt"));
    }
    else if (Operation == TEXT("greater_equal_int"))
    {
        Function = UKismetMathLibrary::StaticClass()->FindFunctionByName(TEXT("GreaterEqual_IntInt"));
    }
    else if (Operation == TEXT("greater_int"))
    {
        Function = UKismetMathLibrary::StaticClass()->FindFunctionByName(TEXT("Greater_IntInt"));
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown math operation: %s"), *Operation));
    }

    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Math function for operation '%s' not found"), *Operation));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(FString::Printf(TEXT("Math operation '%s' node added successfully"), *Operation), NodeId);
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintBranchNode(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    // Create a function call node for Branch
    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = UKismetSystemLibrary::StaticClass()->FindFunctionByName(TEXT("Branch"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Branch function not found"));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(TEXT("Branch node added successfully"), NodeId);
}

// SplineMeshComponent node implementations
TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintAddSplineMeshComponent(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    // Create a function call node for AddComponent (SplineMeshComponent)
    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = AActor::StaticClass()->FindFunctionByName(TEXT("AddComponent"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("AddComponent function not found"));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Set the component class to SplineMeshComponent
    UEdGraphPin* ClassPin = FunctionNode->FindPinChecked(TEXT("Class"));
    if (ClassPin)
    {
        ClassPin->DefaultObject = USplineMeshComponent::StaticClass();
    }

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(TEXT("Add SplineMeshComponent node added successfully"), NodeId);
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSplineMeshSetStartAndEnd(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString SplineMeshComponentName;
    if (!Params->TryGetStringField(TEXT("spline_mesh_component"), SplineMeshComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'spline_mesh_component' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    // Create a function call node for SetStartAndEnd
    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = USplineMeshComponent::StaticClass()->FindFunctionByName(TEXT("SetStartAndEnd"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("SetStartAndEnd function not found"));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(TEXT("SplineMesh SetStartAndEnd node added successfully"), NodeId);
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSplineMeshSetScale(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString SplineMeshComponentName;
    if (!Params->TryGetStringField(TEXT("spline_mesh_component"), SplineMeshComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'spline_mesh_component' parameter"));
    }

    FString ScaleType;
    if (!Params->TryGetStringField(TEXT("scale_type"), ScaleType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'scale_type' parameter (start_scale or end_scale)"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    // Get the event graph
    UEdGraph* EventGraph = FUnrealMCPCommonUtils::GetBlueprintEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Could not find event graph"));
    }

    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    UFunction* Function = nullptr;

    // Determine which scale function to use
    if (ScaleType == TEXT("start_scale"))
    {
        Function = USplineMeshComponent::StaticClass()->FindFunctionByName(TEXT("SetStartScale"));
    }
    else if (ScaleType == TEXT("end_scale"))
    {
        Function = USplineMeshComponent::StaticClass()->FindFunctionByName(TEXT("SetEndScale"));
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown scale_type: %s. Use 'start_scale' or 'end_scale'"), *ScaleType));
    }

    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Scale function for type '%s' not found"), *ScaleType));
    }

    FunctionNode->SetFromFunction(Function);
    
    // Set position if provided
    FString NodePositionStr;
    if (Params->TryGetStringField(TEXT("node_position"), NodePositionStr))
    {
        FVector2D Position = FUnrealMCPCommonUtils::ParseVector2DFromString(NodePositionStr);
        FunctionNode->NodePosX = Position.X;
        FunctionNode->NodePosY = Position.Y;
    }

    // Add to graph
    EventGraph->AddNode(FunctionNode, true);
    FunctionNode->AllocateDefaultPins();

    // Mark blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    FString NodeId = FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID());
    return FUnrealMCPCommonUtils::CreateSuccessResponse(FString::Printf(TEXT("SplineMesh %s node added successfully"), *ScaleType), NodeId);
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSetMobility(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    FString ComponentName = Params->GetStringField(TEXT("component_name"));
    FString NodePosition = Params->GetStringField(TEXT("node_position"));

    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    UEdGraph* EventGraph = FBlueprintEditorUtils::FindEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Event graph not found"));
    }

    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    EventGraph->AddNode(FunctionNode, true, true);

    UFunction* Function = USceneComponent::StaticClass()->FindFunctionByName(TEXT("SetMobility"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("SetMobility function not found"));
    }

    FunctionNode->SetFromFunction(Function);

    // Set position if provided
    if (!NodePosition.IsEmpty())
    {
        TArray<FString> PositionParts;
        NodePosition.ParseIntoArray(PositionParts, TEXT(","));
        if (PositionParts.Num() == 2)
        {
            FunctionNode->NodePosX = FCString::Atoi(*PositionParts[0]);
            FunctionNode->NodePosY = FCString::Atoi(*PositionParts[1]);
        }
    }

    // Reconstruct and mark blueprint as modified
    FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), TEXT("success"));
    Response->SetStringField(TEXT("node_id"), FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID()));
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Added SetMobility node for component '%s'"), *ComponentName));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSetCollisionEnabled(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    FString ComponentName = Params->GetStringField(TEXT("component_name"));
    FString NodePosition = Params->GetStringField(TEXT("node_position"));

    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    UEdGraph* EventGraph = FBlueprintEditorUtils::FindEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Event graph not found"));
    }

    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    EventGraph->AddNode(FunctionNode, true, true);

    UFunction* Function = UPrimitiveComponent::StaticClass()->FindFunctionByName(TEXT("SetCollisionEnabled"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("SetCollisionEnabled function not found"));
    }

    FunctionNode->SetFromFunction(Function);

    // Set position if provided
    if (!NodePosition.IsEmpty())
    {
        TArray<FString> PositionParts;
        NodePosition.ParseIntoArray(PositionParts, TEXT(","));
        if (PositionParts.Num() == 2)
        {
            FunctionNode->NodePosX = FCString::Atoi(*PositionParts[0]);
            FunctionNode->NodePosY = FCString::Atoi(*PositionParts[1]);
        }
    }

    // Reconstruct and mark blueprint as modified
    FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), TEXT("success"));
    Response->SetStringField(TEXT("node_id"), FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID()));
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Added SetCollisionEnabled node for component '%s'"), *ComponentName));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSetCollisionProfile(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    FString ComponentName = Params->GetStringField(TEXT("component_name"));
    FString NodePosition = Params->GetStringField(TEXT("node_position"));

    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    UEdGraph* EventGraph = FBlueprintEditorUtils::FindEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Event graph not found"));
    }

    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    EventGraph->AddNode(FunctionNode, true, true);

    UFunction* Function = UPrimitiveComponent::StaticClass()->FindFunctionByName(TEXT("SetCollisionProfileName"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("SetCollisionProfileName function not found"));
    }

    FunctionNode->SetFromFunction(Function);

    // Set position if provided
    if (!NodePosition.IsEmpty())
    {
        TArray<FString> PositionParts;
        NodePosition.ParseIntoArray(PositionParts, TEXT(","));
        if (PositionParts.Num() == 2)
        {
            FunctionNode->NodePosX = FCString::Atoi(*PositionParts[0]);
            FunctionNode->NodePosY = FCString::Atoi(*PositionParts[1]);
        }
    }

    // Reconstruct and mark blueprint as modified
    FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), TEXT("success"));
    Response->SetStringField(TEXT("node_id"), FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID()));
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Added SetCollisionProfileName node for component '%s'"), *ComponentName));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintSetCanAffectNavigation(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    FString ComponentName = Params->GetStringField(TEXT("component_name"));
    FString NodePosition = Params->GetStringField(TEXT("node_position"));

    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    UEdGraph* EventGraph = FBlueprintEditorUtils::FindEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Event graph not found"));
    }

    UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(EventGraph);
    EventGraph->AddNode(FunctionNode, true, true);

    UFunction* Function = UPrimitiveComponent::StaticClass()->FindFunctionByName(TEXT("SetCanEverAffectNavigation"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("SetCanEverAffectNavigation function not found"));
    }

    FunctionNode->SetFromFunction(Function);

    // Set position if provided
    if (!NodePosition.IsEmpty())
    {
        TArray<FString> PositionParts;
        NodePosition.ParseIntoArray(PositionParts, TEXT(","));
        if (PositionParts.Num() == 2)
        {
            FunctionNode->NodePosX = FCString::Atoi(*PositionParts[0]);
            FunctionNode->NodePosY = FCString::Atoi(*PositionParts[1]);
        }
    }

    // Reconstruct and mark blueprint as modified
    FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), TEXT("success"));
    Response->SetStringField(TEXT("node_id"), FString::Printf(TEXT("%d"), FunctionNode->GetUniqueID()));
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Added SetCanEverAffectNavigation node for component '%s'"), *ComponentName));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintNodeCommands::HandleAddBlueprintArrayAdd(const TSharedPtr<FJsonObject> &Params)
{
    FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    FString ArrayVariable = Params->GetStringField(TEXT("array_variable"));
    FString NodePosition = Params->GetStringField(TEXT("node_position"));

    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprintByName(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found"), *BlueprintName));
    }

    UEdGraph* EventGraph = FBlueprintEditorUtils::FindEventGraph(Blueprint);
    if (!EventGraph)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Event graph not found"));
    }

    UK2Node_CallArrayFunction* ArrayNode = NewObject<UK2Node_CallArrayFunction>(EventGraph);
    EventGraph->AddNode(ArrayNode, true, true);

    UFunction* Function = UKismetArrayLibrary::StaticClass()->FindFunctionByName(TEXT("Array_Add"));
    if (!Function)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Array_Add function not found"));
    }

    ArrayNode->SetFromFunction(Function);

    // Set position if provided
    if (!NodePosition.IsEmpty())
    {
        TArray<FString> PositionParts;
        NodePosition.ParseIntoArray(PositionParts, TEXT(","));
        if (PositionParts.Num() == 2)
        {
            ArrayNode->NodePosX = FCString::Atoi(*PositionParts[0]);
            ArrayNode->NodePosY = FCString::Atoi(*PositionParts[1]);
        }
    }

    // Reconstruct and mark blueprint as modified
    FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), TEXT("success"));
    Response->SetStringField(TEXT("node_id"), FString::Printf(TEXT("%d"), ArrayNode->GetUniqueID()));
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Added Array Add node for array '%s'"), *ArrayVariable));

    return Response;
}